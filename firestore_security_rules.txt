rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isValidUser() {
      return isAuthenticated() && request.auth.token.email_verified == true;
    }

    // Check if this is a new user creation (basic user data only)
    function isBasicUserCreation() {
      return isAuthenticated() &&
        request.resource.data.keys().hasAll(['id', 'email', 'name', 'createdAt', 'lastActive']) &&
        request.resource.data.keys().size() <= 6; // Allow up to 6 fields (including optional preferences)
    }

    // Prevent credit manipulation from client side
    function creditsNotModified() {
      return !('credits' in request.resource.data.diff(resource.data).affectedKeys());
    }

    // Prevent subscription manipulation from client side
    function subscriptionNotModified() {
      return !('subscription' in request.resource.data.diff(resource.data).affectedKeys());
    }

    // Prevent payment info manipulation from client side
    function paymentNotModified() {
      return !('payment' in request.resource.data.diff(resource.data).affectedKeys());
    }

    // User documents - enhanced security for subscription system
    match /users/{userId} {
      // Read access: owner can read their data OR admin can read all users
      allow read: if isOwner(userId) || (isAuthenticated() &&
        request.auth.token.email in ['<EMAIL>', '<EMAIL>']);

      // Create access: allow user creation during signup
      allow create: if isOwner(userId);

      // Update access: owner can update their data OR admin can update any user
      allow update: if (isOwner(userId) && (
        // Allow updates during user initialization/migration
        ('migratedAt' in request.resource.data) ||
        // Allow normal profile updates (excluding sensitive fields)
        (!('credits' in request.resource.data.diff(resource.data).affectedKeys()) &&
         !('payment' in request.resource.data.diff(resource.data).affectedKeys())) ||
        // Allow subscription updates through proper channels
        ('subscription' in request.resource.data &&
         request.resource.data.subscription.plan in ['free', 'basic', 'premium'])
      )) || (isAuthenticated() &&
        request.auth.token.email in ['<EMAIL>', '<EMAIL>']);

      // Delete access: admin only
      allow delete: if isAuthenticated() &&
        request.auth.token.email in ['<EMAIL>', '<EMAIL>'];
    }
    
    // Chat messages - only owner can access their messages
    match /chat_messages/{messageId} {
      allow read: if isAuthenticated() &&
        request.auth.uid == resource.data.userId;
      allow create: if isAuthenticated() &&
        request.auth.uid == request.resource.data.userId;
      allow update: if isAuthenticated() &&
        request.auth.uid == resource.data.userId;
      allow delete: if isAuthenticated() &&
        request.auth.uid == resource.data.userId;
    }

    // User conversations - only owner can access
    match /user_conversations/{userId} {
      allow read, write: if isOwner(userId);

      match /conversations/{conversationId} {
        allow read, write: if isOwner(userId);

        match /messages/{messageId} {
          allow read, write: if isOwner(userId);
        }
      }
    }
    
    // Voice sessions - only owner can access
    match /voice_sessions/{sessionId} {
      allow read: if isAuthenticated() && 
        request.auth.uid == resource.data.userId;
      
      // Only cloud functions can write voice sessions
      allow write: if false;
    }
    
    // Usage logs - read only for user, write only for cloud functions
    match /usage_logs/{logId} {
      allow read: if isAuthenticated() && 
        request.auth.uid == resource.data.userId;
      
      // Only cloud functions can write usage logs
      allow write: if false;
    }
    
    // Subscription plans configuration (read-only for all authenticated users)
    match /subscription_plans/{planId} {
      allow read: if isAuthenticated();
      allow write: if false; // Only admin/cloud functions can modify
    }
    
    // Payment transactions (read-only for owner, write-only for cloud functions)
    match /payment_transactions/{transactionId} {
      allow read: if isAuthenticated() && 
        request.auth.uid == resource.data.userId;
      
      // Only cloud functions can write payment transactions
      allow write: if false;
    }
    
    // Admin collection (for app settings and configuration)
    match /admin/{document} {
      allow read: if isAuthenticated();
      allow write: if false; // Only cloud functions can write
    }

    // App config collection (admin access)
    match /app_config/{document} {
      allow read, write: if isAuthenticated() &&
        request.auth.token.email in ['<EMAIL>', '<EMAIL>'];
    }


    
    // Webhook logs (admin only)
    match /webhook_logs/{logId} {
      allow read, write: if false; // Only cloud functions
    }
    
    // Analytics data (admin only)
    match /analytics/{document} {
      allow read, write: if false; // Only cloud functions
    }
    
    // Error logs (admin only)
    match /error_logs/{logId} {
      allow read, write: if false; // Only cloud functions
    }

    // Analytics events collection - allow authenticated users to write their own events
    match /analytics_events/{eventId} {
      allow read: if isAuthenticated() &&
        request.auth.uid == resource.data.userId;
      allow create: if isAuthenticated() &&
        request.auth.uid == request.resource.data.userId;
      allow update: if isAuthenticated() &&
        request.auth.uid == resource.data.userId;
      allow delete: if false; // Don't allow deletion of analytics events
    }

    // Catch-all rule - deny everything else
    match /{document=**} {
      allow read, write: if false;
    }

    // Admin logs collection
    match /admin_logs/{docId} {
      allow read, write: if isAuthenticated() &&
        request.auth.token.email in ['<EMAIL>', '<EMAIL>'];
    }

    // Subscriptions collection (admin access for user management)
    match /subscriptions/{userId} {
      // Users can read their own subscription
      allow read: if isOwner(userId);

      // Admin can read and update all subscriptions
      allow read, write: if isAuthenticated() &&
        request.auth.token.email in ['<EMAIL>', '<EMAIL>'];
    }

    // User notifications collection (admin can create notifications)
    match /user_notifications/{notificationId} {
      // Users can read their own notifications
      allow read: if isAuthenticated() &&
        request.auth.uid == resource.data.userId;

      // Users can delete their own old notifications (for cleanup)
      allow delete: if isAuthenticated() &&
        request.auth.uid == resource.data.userId;

      // Admin can create notifications for users
      allow create: if isAuthenticated() &&
        request.auth.token.email in ['<EMAIL>', '<EMAIL>'];
    }

    // Real-time updates collection (admin can create updates)
    match /real_time_updates/{userId} {
      // Users can read their own updates
      allow read: if isOwner(userId);

      // Admin can create/update real-time notifications
      allow write: if isAuthenticated() &&
        request.auth.token.email in ['<EMAIL>', '<EMAIL>'];
    }

    // Global notifications collection (admin only)
    match /global_notifications/{notificationId} {
      // All authenticated users can read global notifications
      allow read: if isAuthenticated();

      // Only admin can create global notifications
      allow write: if isAuthenticated() &&
        request.auth.token.email in ['<EMAIL>', '<EMAIL>'];
    }

    // Status collection (for presence/active user tracking)
    match /status/{userId} {
      // Users can read/write their own status
      allow read, write: if isOwner(userId);

      // Admin can read all status documents
      allow read: if isAuthenticated() &&
        request.auth.token.email in ['<EMAIL>', '<EMAIL>'];
    }
  }
}

// Additional security notes:
// 1. All credit modifications must go through Cloud Functions
// 2. All subscription changes must go through Cloud Functions
// 3. All payment processing must go through Cloud Functions
// 4. Users can only read/write their own data
// 5. Sensitive operations are logged for audit purposes
// 6. Email verification is required for most operations
// 7. Admin collections are restricted to hardcoded admin emails only
