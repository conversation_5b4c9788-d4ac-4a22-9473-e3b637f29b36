import 'package:flight_fear_wellness_app/blocs/auth/auth_event.dart';
import 'package:flight_fear_wellness_app/screens/chat/chat_screen.dart';
import 'package:flight_fear_wellness_app/screens/voice/voice_screen.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flight_fear_wellness_app/blocs/auth/auth_bloc.dart';
import 'package:flight_fear_wellness_app/widgets/notification_bell.dart';
import 'package:flight_fear_wellness_app/utils/theme.dart';
import 'package:flight_fear_wellness_app/screens/breathing/breathing_screen.dart';
import 'package:flight_fear_wellness_app/services/secure_storage_service.dart';
import 'package:flight_fear_wellness_app/providers/subscription_provider.dart';
import 'package:flight_fear_wellness_app/services/plan_expiration_service.dart';
import 'package:flight_fear_wellness_app/models/subscription_model.dart';
import 'package:flight_fear_wellness_app/services/active_session_service.dart';
import 'package:flight_fear_wellness_app/services/notification_service.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  // Purpose: Build feature card widget for home screen navigation
  Widget _buildFeatureCard({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String description,
    required VoidCallback onTap,
    required Color color,
  }) {
    return Card(
      elevation: 0,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: const BorderSide(color: AppTheme.borderColor, width: 1),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, size: 28, color: color),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w700,
                          ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: Theme.of(context).textTheme.bodyMedium,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, size: 16, color: color),
            ],
          ),
        ),
      ),
    );
  }

  // Purpose: Build credit display pill showing remaining credits for each feature
  Widget _buildCreditPill(String label, int value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            value.toString(),
            style: TextStyle(
              fontWeight: FontWeight.w700,
              fontSize: 16,
              color: color,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: AppTheme.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  // Purpose: Show logout confirmation dialog to user
  Future<void> _showLogoutDialog(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Logout?',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                const Text('Are you sure you want to logout?'),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      child: const Text('Cancel'),
                    ),
                    const SizedBox(width: 8),
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(true),
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.red,
                      ),
                      child: const Text('Logout'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );

    if (result == true) {
      await SecureStorageService.setAppSessionState(AppSessionState.logoutLogin);
      if (context.mounted) {
        context.read<AuthBloc>().add(SignOutEvent());
      }
    }
  }

  // Purpose: Build main home screen UI with feature cards and user information
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.only(top: 48, left: 24, right: 24, bottom: 16),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.08),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(24),
                bottomRight: Radius.circular(24),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                )
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('Alora', style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.w800,
                      color: AppTheme.textPrimary,
                    )),
                    Row(
                      children: [
                        const NotificationBell(),
                        IconButton(
                          icon: const Icon(Icons.logout),
                          onPressed: () => _showLogoutDialog(context),
                          tooltip: 'Logout',
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  'Welcome!',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                ),
                const SizedBox(height: 8),
                Text(
                  'I am here to help you.',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 16),
                
                Consumer<SubscriptionProvider>(
                  builder: (context, subscriptionProvider, child) {
                    if (!subscriptionProvider.isInitialized) {
                      return const SizedBox(
                        height: 80,
                        child: Center(child: CircularProgressIndicator()),
                      );
                    }
                    
                    return Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: AppTheme.borderColor, width: 1),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                                decoration: BoxDecoration(
                                  color: AppTheme.primaryColor.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: AppTheme.primaryColor),
                                ),
                                child: Text(
                                  subscriptionProvider.planName,
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    color: AppTheme.primaryColor,
                                  ),
                                ),
                              ),
                              const Spacer(),
                              if (subscriptionProvider.currentPlan == SubscriptionPlan.free)
                                Text(
                                  '${subscriptionProvider.daysRemaining} days left',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    color: subscriptionProvider.daysRemaining <= 2
                                        ? Colors.red
                                        : AppTheme.primaryColor,
                                  ),
                                ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              _buildCreditPill(
                                'Chat',
                                subscriptionProvider.chatCreditsRemaining,
                                AppTheme.primaryColor,
                              ),
                              _buildCreditPill(
                                'Voice',
                                subscriptionProvider.voiceCreditsRemaining,
                                const Color(0xFF4CAF50),
                              ),
                            ],
                          ),
                        ],
                      ),
                    );
                  },
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
          
          Expanded(
            child: Container(
              color: AppTheme.backgroundColor,
              padding: const EdgeInsets.all(24),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Manage Anxiety',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w700,
                          ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Select an option to get started',
                      style: TextStyle(color: AppTheme.textSecondary),
                    ),
                    const SizedBox(height: 24),
                    
                    Consumer<SubscriptionProvider>(
                      builder: (context, subscriptionProvider, child) {
                        return _buildFeatureCard(
                          context: context,
                          icon: Icons.chat_bubble_outline_rounded,
                          title: 'Alora Chat',
                          description: 'Talk to Alora about your flight fears',
                          color: AppTheme.primaryColor,
                          onTap: () async {
                            if (!subscriptionProvider.isInitialized) {
                              await subscriptionProvider.initialize();
                            }

                            if (context.mounted) {
                              final canAccess = await PlanExpirationService.blockFeatureAccess(
                                context,
                                subscriptionProvider.userSubscription,
                                'chat',
                              );

                              if (canAccess) {
                                ActiveSessionService().markUserActive();

                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const ChatScreen(),
                                  ),
                                );
                              }
                            }
                          },
                        );
                      },
                    ),
                    
                    Consumer<SubscriptionProvider>(
                      builder: (context, subscriptionProvider, child) {
                        return _buildFeatureCard(
                          context: context,
                          icon: Icons.mic_outlined,
                          title: 'Alora Voice',
                          description: 'Voice conversation with Alora',
                          color: const Color(0xFF4CAF50),
                          onTap: () async {
                            if (!subscriptionProvider.isInitialized) {
                              await subscriptionProvider.initialize();
                            }

                            if (context.mounted) {
                              final canAccess = await PlanExpirationService.blockFeatureAccess(
                                context,
                                subscriptionProvider.userSubscription,
                                'voice',
                              );

                              if (canAccess) {
                                ActiveSessionService().markUserActive();

                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const VoiceScreen(),
                                  ),
                                );
                              }
                            }
                          },
                        );
                      },
                    ),
                    
                    _buildFeatureCard(
                      context: context,
                      icon: Icons.self_improvement_rounded,
                      title: 'Breathing Exercises',
                      description: 'Calm your nerves with guided exercises',
                      color: const Color(0xFF9C27B0),
                      onTap: () {
                        ActiveSessionService().markUserActive();

                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const BreathingScreen(),
                          ),
                        );
                      },
                    ),
                    
                   
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      // Temporary floating action button for testing real-time notifications
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          await NotificationService.createRealTimeTestNotification();
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Real-time test notification sent! Check notification bell.'),
                duration: Duration(seconds: 2),
              ),
            );
          }
        },
        backgroundColor: AppTheme.primaryColor,
        tooltip: 'Test Real-Time Notifications',
        child: const Icon(Icons.notifications_active, color: Colors.white),
      ),
    );
  }
}