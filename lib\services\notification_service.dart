import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import '../models/notification_model.dart';


class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  static const String _notificationsCollection = 'user_notifications';

  Future<String?> createNotification(NotificationModel notification) async {
    try {
      final docRef = await _firestore
          .collection(_notificationsCollection)
          .add(notification.toFirestore());
      
      debugPrint('NotificationService: Created notification ${docRef.id}');
      return docRef.id;
    } catch (e) {
      debugPrint('NotificationService: Error creating notification: $e');
      return null;
    }
  }

  Stream<List<NotificationModel>> getUserNotifications({int? limit}) {
    final userId = _auth.currentUser?.uid;
    if (userId == null) {
      debugPrint('NotificationService: No user ID available - user not authenticated');
      return Stream.value([]);
    }

    debugPrint('NotificationService: Getting notifications for user: $userId from collection: $_notificationsCollection');

    try {
      Query query = _firestore
          .collection(_notificationsCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true);

      if (limit != null) {
        query = query.limit(limit);
      }

      return query.snapshots().map((snapshot) {
        debugPrint('NotificationService: Received ${snapshot.docs.length} notifications from Firestore');
        final notifications = <NotificationModel>[];

        for (final doc in snapshot.docs) {
          try {
            final notification = NotificationModel.fromFirestore(doc);
            notifications.add(notification);
            debugPrint('NotificationService: Parsed notification: ${notification.title}');
          } catch (e) {
            debugPrint('NotificationService: Error parsing notification ${doc.id}: $e');
          }
        }

        return notifications;
      }).handleError((error) {
        debugPrint('NotificationService: Stream error: $error');
        // Check if it's a permission error
        if (error.toString().contains('permission-denied')) {
          debugPrint('NotificationService: Permission denied - check Firestore security rules');
        }
        throw error;
      });
    } catch (e) {
      debugPrint('NotificationService: Error setting up stream: $e');
      return Stream.error(e);
    }
  }

  Stream<int> getUnreadCount() {
    final userId = _auth.currentUser?.uid;
    if (userId == null) {
      return Stream.value(0);
    }

    return _firestore
        .collection(_notificationsCollection)
        .where('userId', isEqualTo: userId)
        .where('isRead', isEqualTo: false)
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }

  Future<bool> markAsRead(String notificationId) async {
    try {
      await _firestore
          .collection(_notificationsCollection)
          .doc(notificationId)
          .update({
        'isRead': true,
        'readAt': Timestamp.fromDate(DateTime.now()),
      });
      
      debugPrint('NotificationService: Marked notification $notificationId as read');
      return true;
    } catch (e) {
      debugPrint('NotificationService: Error marking notification as read: $e');
      return false;
    }
  }

  Future<bool> markAllAsRead() async {
    final userId = _auth.currentUser?.uid;
    if (userId == null) return false;

    try {
      final batch = _firestore.batch();
      final unreadNotifications = await _firestore
          .collection(_notificationsCollection)
          .where('userId', isEqualTo: userId)
          .where('isRead', isEqualTo: false)
          .get();

      for (final doc in unreadNotifications.docs) {
        batch.update(doc.reference, {
          'isRead': true,
          'readAt': Timestamp.fromDate(DateTime.now()),
        });
      }

      await batch.commit();
      debugPrint('NotificationService: Marked ${unreadNotifications.docs.length} notifications as read');
      return true;
    } catch (e) {
      debugPrint('NotificationService: Error marking all notifications as read: $e');
      return false;
    }
  }

  Future<bool> deleteNotification(String notificationId) async {
    try {
      await _firestore
          .collection(_notificationsCollection)
          .doc(notificationId)
          .delete();
      
      debugPrint('NotificationService: Deleted notification $notificationId');
      return true;
    } catch (e) {
      debugPrint('NotificationService: Error deleting notification: $e');
      return false;
    }
  }

  Future<bool> deleteAllNotifications() async {
    final userId = _auth.currentUser?.uid;
    if (userId == null) return false;

    try {
      final batch = _firestore.batch();
      final userNotifications = await _firestore
          .collection(_notificationsCollection)
          .where('userId', isEqualTo: userId)
          .get();

      for (final doc in userNotifications.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      debugPrint('NotificationService: Deleted ${userNotifications.docs.length} notifications');
      return true;
    } catch (e) {
      debugPrint('NotificationService: Error deleting all notifications: $e');
      return false;
    }
  }


  Future<void> sendWelcomeNotification(String userId) async {
    final notification = NotificationFactory.createWelcomeNotification(userId);
    await createNotification(notification);
  }

  Future<void> sendSubscriptionChangeNotification(
    String userId,
    String fromPlan,
    String toPlan,
    Map<String, int> newCredits,
  ) async {
    final notification = NotificationFactory.createSubscriptionChangeNotification(
      userId,
      fromPlan,
      toPlan,
      newCredits,
    );
    await createNotification(notification);
  }

  Future<void> sendCreditWarningNotification(
    String userId,
    String creditType,
    int remaining,
    int total,
  ) async {
    final recentWarning = await _hasRecentCreditWarning(userId, creditType);
    if (recentWarning) return;

    final notification = NotificationFactory.createCreditWarningNotification(
      userId,
      creditType,
      remaining,
      total,
    );
    await createNotification(notification);
  }

  Future<void> sendPaymentConfirmationNotification(
    String userId,
    String planName,
    double amount,
    Map<String, int> allocatedCredits,
  ) async {
    final notification = NotificationFactory.createPaymentConfirmationNotification(
      userId,
      planName,
      amount,
      allocatedCredits,
    );
    await createNotification(notification);
  }

  Future<void> sendPlanAccessUpdateNotification(
    String userId,
    String planName,
    List<String> newFeatures,
  ) async {
    final notification = NotificationFactory.createPlanAccessUpdateNotification(
      userId,
      planName,
      newFeatures,
    );
    await createNotification(notification);
  }

  Future<bool> _hasRecentCreditWarning(String userId, String creditType) async {
    try {
      final twentyFourHoursAgo = DateTime.now().subtract(const Duration(hours: 24));
      
      final recentWarnings = await _firestore
          .collection(_notificationsCollection)
          .where('userId', isEqualTo: userId)
          .where('type', isEqualTo: NotificationType.creditWarning.name)
          .where('createdAt', isGreaterThan: Timestamp.fromDate(twentyFourHoursAgo))
          .get();

      for (final doc in recentWarnings.docs) {
        final data = doc.data();
        final metadata = data['metadata'] as Map<String, dynamic>?;
        if (metadata?['creditType'] == creditType) {
          return true;
        }
      }

      return false;
    } catch (e) {
      debugPrint('NotificationService: Error checking recent credit warnings: $e');
      return false;
    }
  }

  Future<void> initializeForUser(String userId, {bool isNewUser = false}) async {
    debugPrint('NotificationService: Initializing for user: $userId, isNewUser: $isNewUser');

    if (isNewUser) {
      await Future.delayed(const Duration(seconds: 2));
      await sendWelcomeNotification(userId);
      debugPrint('NotificationService: Welcome notification sent for new user: $userId');
    }

    // Clean up old notifications (older than 30 days)
    await cleanupOldNotifications();
  }

  Future<void> cleanupOldNotifications() async {
    try {
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));

      final oldNotifications = await _firestore
          .collection(_notificationsCollection)
          .where('createdAt', isLessThan: Timestamp.fromDate(thirtyDaysAgo))
          .get();

      final batch = _firestore.batch();
      for (final doc in oldNotifications.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
      debugPrint('NotificationService: Cleaned up ${oldNotifications.docs.length} old notifications');
    } catch (e) {
      debugPrint('NotificationService: Error cleaning up old notifications: $e');
    }
  }

  // Test method to create a sample notification for debugging
  Future<void> createTestNotification() async {
    final userId = _auth.currentUser?.uid;
    if (userId == null) return;

    final testNotification = NotificationModel(
      id: '',
      userId: userId,
      title: 'Test Notification',
      message: 'This is a test notification to verify the system is working.',
      type: NotificationType.general,
      priority: NotificationPriority.normal,
      createdAt: DateTime.now(),
    );

    await createNotification(testNotification);
    debugPrint('NotificationService: Created test notification for user: $userId');
  }

  // Method to create a real-time test notification (for debugging real-time functionality)
  static Future<void> createRealTimeTestNotification() async {
    final instance = NotificationService();
    final userId = instance._auth.currentUser?.uid;
    if (userId == null) {
      debugPrint('NotificationService: No authenticated user for real-time test');
      return;
    }

    final testNotification = NotificationModel(
      id: '',
      userId: userId,
      title: 'Real-Time Test 🚀',
      message: 'This notification should appear instantly without refresh! Time: ${DateTime.now().toLocal().toString().split('.')[0]}',
      type: NotificationType.general,
      priority: NotificationPriority.high,
      createdAt: DateTime.now(),
      metadata: {
        'testType': 'realtime',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      },
    );

    await instance.createNotification(testNotification);
    debugPrint('NotificationService: Created real-time test notification for user: $userId');
  }
}
