import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';

// Purpose: Advanced AI optimization algorithms for voice processing performance
class AIOptimizationService {
  static final AIOptimizationService _instance = AIOptimizationService._internal();
  factory AIOptimizationService() => _instance;
  AIOptimizationService._internal();

  // Performance metrics tracking
  final Map<String, List<double>> _responseTimeHistory = {};
  final Map<String, double> _userPreferenceScores = {};
  final Map<String, int> _featureUsageCount = {};
  
  // Adaptive token optimization
  static const Map<String, double> _baseTokenMultipliers = {
    'severely_distressed': 1.6,
    'anxious': 1.3,
    'frustrated': 1.1,
    'stressed': 1.0,
    'normal': 0.8,
  };

  // Purpose: Dynamically optimize token limits based on user patterns and response quality
  int optimizeTokenLimit(String moodLevel, String userId, List<String> recentResponses) {
    final baseLimit = _getBaseTokenLimit(moodLevel);
    final userMultiplier = _calculateUserSpecificMultiplier(userId, recentResponses);
    final qualityMultiplier = _calculateQualityMultiplier(recentResponses);
    
    final optimizedLimit = (baseLimit * userMultiplier * qualityMultiplier).round();
    return optimizedLimit.clamp(30, 150); // Ensure reasonable bounds
  }

  int _getBaseTokenLimit(String moodLevel) {
    switch (moodLevel) {
      case 'severely_distressed': return 80;
      case 'anxious': return 65;
      case 'frustrated': return 60;
      case 'stressed': return 55;
      default: return 50;
    }
  }

  double _calculateUserSpecificMultiplier(String userId, List<String> recentResponses) {
    // Analyze user's response length preferences
    if (recentResponses.isEmpty) return 1.0;
    
    final avgResponseLength = recentResponses
        .map((r) => r.split(' ').length)
        .reduce((a, b) => a + b) / recentResponses.length;
    
    // Users who engage with longer responses get slightly more tokens
    if (avgResponseLength > 25) return 1.2;
    if (avgResponseLength > 15) return 1.1;
    if (avgResponseLength < 8) return 0.9;
    return 1.0;
  }

  double _calculateQualityMultiplier(List<String> recentResponses) {
    if (recentResponses.isEmpty) return 1.0;
    
    double qualityScore = 0.0;
    
    for (final response in recentResponses) {
      // Check for completeness (no cut-off sentences)
      if (response.endsWith('.') || response.endsWith('!') || response.endsWith('?')) {
        qualityScore += 0.3;
      }
      
      // Check for variety in sentence structure
      final sentences = response.split(RegExp(r'[.!?]'));
      if (sentences.length > 1) qualityScore += 0.2;
      
      // Check for engagement (questions, empathy)
      if (response.contains('?')) qualityScore += 0.2;
      if (response.toLowerCase().contains(RegExp(r'\b(understand|feel|sounds|tell me)\b'))) {
        qualityScore += 0.3;
      }
    }
    
    final avgQuality = qualityScore / recentResponses.length;
    
    // If quality is low, increase tokens slightly to allow for better responses
    if (avgQuality < 0.5) return 1.15;
    if (avgQuality > 0.8) return 0.95; // High quality, can use fewer tokens
    return 1.0;
  }

  // Purpose: Predict optimal voice model parameters based on user interaction patterns
  Map<String, dynamic> optimizeVoiceParameters(String userId, String moodLevel) {
    final userPreference = _userPreferenceScores[userId] ?? 0.5;
    
    return {
      'stability': _optimizeStability(moodLevel, userPreference),
      'similarity_boost': _optimizeSimilarityBoost(moodLevel, userPreference),
      'style': _optimizeStyle(moodLevel),
      'optimize_streaming_latency': _optimizeLatency(moodLevel),
    };
  }

  double _optimizeStability(String moodLevel, double userPreference) {
    final baseStability = {
      'severely_distressed': 0.4, // More stable for crisis
      'anxious': 0.35,
      'frustrated': 0.3,
      'stressed': 0.3,
      'normal': 0.25,
    }[moodLevel] ?? 0.3;
    
    // Adjust based on user preference for consistency vs. expressiveness
    return (baseStability + (userPreference * 0.2)).clamp(0.1, 0.8);
  }

  double _optimizeSimilarityBoost(String moodLevel, double userPreference) {
    final baseSimilarity = {
      'severely_distressed': 0.6, // More consistent voice for comfort
      'anxious': 0.55,
      'frustrated': 0.5,
      'stressed': 0.5,
      'normal': 0.45,
    }[moodLevel] ?? 0.5;
    
    return (baseSimilarity + (userPreference * 0.15)).clamp(0.3, 0.8);
  }

  double _optimizeStyle(String moodLevel) {
    // Style parameter for emotional expression
    switch (moodLevel) {
      case 'severely_distressed': return 0.1; // Calm, steady
      case 'anxious': return 0.15;
      case 'frustrated': return 0.2;
      case 'stressed': return 0.1;
      default: return 0.0;
    }
  }

  int _optimizeLatency(String moodLevel) {
    // Higher latency optimization for crisis situations (faster response)
    switch (moodLevel) {
      case 'severely_distressed': return 4;
      case 'anxious': return 3;
      default: return 2;
    }
  }

  // Purpose: Adaptive prompt optimization based on conversation success patterns
  String optimizeSystemPrompt(String basePrompt, String userId, String moodLevel, 
      List<Map<String, dynamic>> conversationHistory) {
    
    final userEngagement = _calculateEngagementScore(conversationHistory);
    final conversationLength = conversationHistory.length;
    
    String optimizedPrompt = basePrompt;
    
    // Add engagement boosters for low-engagement users
    if (userEngagement < 0.3 && conversationLength > 3) {
      optimizedPrompt += '\n\nEXTRA ENGAGEMENT: Ask more specific, personal questions about their interests and experiences to build stronger connection.';
    }
    
    // Add brevity instructions for users who prefer short responses
    if (_userPreferenceScores[userId] != null && _userPreferenceScores[userId]! < 0.3) {
      optimizedPrompt += '\n\nBREVITY MODE: Keep responses to 1 sentence maximum while maintaining warmth.';
    }
    
    // Add crisis-specific optimizations
    if (moodLevel == 'severely_distressed') {
      optimizedPrompt += '\n\nCRISIS OPTIMIZATION: Prioritize immediate comfort and grounding. Use shorter, more frequent responses.';
    }
    
    return optimizedPrompt;
  }

  double _calculateEngagementScore(List<Map<String, dynamic>> history) {
    if (history.length < 2) return 0.5;
    
    double engagementScore = 0.0;
    int userMessages = 0;
    
    for (final msg in history) {
      if (msg['role'] == 'user') {
        userMessages++;
        final content = msg['content'].toString();
        
        // Longer messages indicate higher engagement
        if (content.length > 50) engagementScore += 0.3;
        if (content.length > 100) engagementScore += 0.2;
        
        // Questions indicate engagement
        if (content.contains('?')) engagementScore += 0.2;
        
        // Personal sharing indicates engagement
        if (content.toLowerCase().contains(RegExp(r'\b(i feel|i think|i am|my)\b'))) {
          engagementScore += 0.3;
        }
      }
    }
    
    return userMessages > 0 ? engagementScore / userMessages : 0.0;
  }

  // Purpose: Track and learn from user interaction patterns
  void trackUserInteraction(String userId, String feature, double satisfactionScore) {
    _userPreferenceScores[userId] = satisfactionScore;
    _featureUsageCount[feature] = (_featureUsageCount[feature] ?? 0) + 1;
  }

  void trackResponseTime(String operation, double timeMs) {
    _responseTimeHistory[operation] ??= [];
    _responseTimeHistory[operation]!.add(timeMs);
    
    // Keep only recent 50 measurements
    if (_responseTimeHistory[operation]!.length > 50) {
      _responseTimeHistory[operation]!.removeAt(0);
    }
  }

  // Purpose: Get performance insights and recommendations
  Map<String, dynamic> getPerformanceInsights() {
    final insights = <String, dynamic>{};
    
    for (final entry in _responseTimeHistory.entries) {
      final times = entry.value;
      if (times.isNotEmpty) {
        insights['${entry.key}_avg_time'] = times.reduce((a, b) => a + b) / times.length;
        insights['${entry.key}_p95_time'] = _calculatePercentile(times, 0.95);
      }
    }
    
    insights['total_feature_usage'] = _featureUsageCount;
    insights['avg_user_satisfaction'] = _userPreferenceScores.values.isNotEmpty 
        ? _userPreferenceScores.values.reduce((a, b) => a + b) / _userPreferenceScores.length 
        : 0.0;
    
    return insights;
  }

  double _calculatePercentile(List<double> values, double percentile) {
    final sorted = List<double>.from(values)..sort();
    final index = (sorted.length * percentile).floor();
    return sorted[index.clamp(0, sorted.length - 1)];
  }

  // Purpose: Clear old data to prevent memory leaks
  void cleanup() {
    final now = DateTime.now();
    
    // Clear old response time data (keep only last hour of data)
    _responseTimeHistory.clear();
    
    // Clear user preferences older than 30 days (implement if needed)
    // This would require timestamp tracking
  }
}
